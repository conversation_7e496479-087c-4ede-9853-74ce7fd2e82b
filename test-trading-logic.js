// Test script to verify trading logic and fee calculations
// Run this in the browser console when the app is loaded

console.log('🧪 Testing Trading Logic and Fee Calculations');

// Test constants
const TRADING_FEE_PERCENT = 0.0025; // 0.25%
const INITIAL_BALANCE = 500;

// Mock asset for testing
const mockAsset = {
  symbol: 'TEST-EUR',
  name: 'Test Asset',
  price: 100, // €100 per unit
  priceChange24h: 0
};

console.log('\n📊 Test Scenario 1: BUY Transaction');
console.log('Initial balance: €500');
console.log('Asset price: €100');
console.log('Amount to buy: 2 units');

const buyAmount = 2;
const buyPrice = mockAsset.price;
const buyTotal = buyAmount * buyPrice; // €200
const buyFee = buyTotal * TRADING_FEE_PERCENT; // €0.50
const buyTotalCost = buyTotal + buyFee; // €200.50

console.log(`Total cost: €${buyTotal}`);
console.log(`Fee (0.25%): €${buyFee.toFixed(3)}`);
console.log(`Total deducted from balance: €${buyTotalCost.toFixed(3)}`);
console.log(`Remaining balance: €${(INITIAL_BALANCE - buyTotalCost).toFixed(3)}`);

console.log('\n📊 Test Scenario 2: SELL Transaction');
console.log('Holdings: 2 units');
console.log('Asset price: €110 (10% increase)');
console.log('Amount to sell: 2 units');

const sellAmount = 2;
const sellPrice = 110;
const sellTotal = sellAmount * sellPrice; // €220
const sellFee = sellTotal * TRADING_FEE_PERCENT; // €0.55
const sellNetReceived = sellTotal - sellFee; // €219.45

console.log(`Gross proceeds: €${sellTotal}`);
console.log(`Fee (0.25%): €${sellFee.toFixed(3)}`);
console.log(`Net received: €${sellNetReceived.toFixed(3)}`);

// Calculate profit
const avgBuyPrice = buyPrice; // €100
const profit = (sellPrice - avgBuyPrice) * sellAmount - sellFee;
console.log(`Profit calculation: (€${sellPrice} - €${avgBuyPrice}) × ${sellAmount} - €${sellFee.toFixed(3)} = €${profit.toFixed(3)}`);

console.log('\n📊 Final Portfolio State');
const finalBalance = (INITIAL_BALANCE - buyTotalCost) + sellNetReceived;
console.log(`Final balance: €${finalBalance.toFixed(3)}`);
console.log(`Total fees paid: €${(buyFee + sellFee).toFixed(3)}`);
console.log(`Net profit: €${(finalBalance - INITIAL_BALANCE).toFixed(3)}`);

console.log('\n🔍 Verification Functions');

// Function to verify a trade in the actual app
window.verifyTrade = function(trade) {
  console.log('\n🔍 Verifying trade:', trade);
  
  const expectedFee = trade.total * TRADING_FEE_PERCENT;
  const feeCorrect = Math.abs(trade.fee - expectedFee) < 0.001;
  
  console.log(`Expected fee: €${expectedFee.toFixed(3)}`);
  console.log(`Actual fee: €${trade.fee.toFixed(3)}`);
  console.log(`Fee calculation correct: ${feeCorrect ? '✅' : '❌'}`);
  
  if (trade.type === 'BUY') {
    console.log(`Total cost should be: €${(trade.total + trade.fee).toFixed(3)}`);
  } else {
    console.log(`Net received should be: €${(trade.total - trade.fee).toFixed(3)}`);
  }
  
  return feeCorrect;
};

// Function to check portfolio consistency
window.checkPortfolio = function() {
  const portfolio = JSON.parse(localStorage.getItem('cts_portfolio_v1') || '{}');
  const trades = JSON.parse(localStorage.getItem('cts_trades_v1') || '[]');
  
  console.log('\n📊 Portfolio Consistency Check');
  console.log('Current portfolio:', portfolio);
  
  let calculatedBalance = INITIAL_BALANCE;
  let totalFeesPaid = 0;
  
  trades.forEach(trade => {
    totalFeesPaid += trade.fee;
    if (trade.type === 'BUY') {
      calculatedBalance -= (trade.total + trade.fee);
    } else {
      calculatedBalance += (trade.total - trade.fee);
    }
  });
  
  console.log(`Calculated balance: €${calculatedBalance.toFixed(3)}`);
  console.log(`Actual balance: €${portfolio.balance?.toFixed(3) || 'N/A'}`);
  console.log(`Total fees paid: €${totalFeesPaid.toFixed(3)}`);
  
  const balanceCorrect = Math.abs(calculatedBalance - (portfolio.balance || 0)) < 0.001;
  console.log(`Balance calculation correct: ${balanceCorrect ? '✅' : '❌'}`);
  
  return { calculatedBalance, actualBalance: portfolio.balance, totalFeesPaid, balanceCorrect };
};

// Function to simulate a complete buy-sell cycle
window.testTradingCycle = function() {
  console.log('\n🔄 Testing Complete Trading Cycle');

  // Get current portfolio state
  const initialPortfolio = JSON.parse(localStorage.getItem('cts_portfolio_v1') || '{}');
  const initialBalance = initialPortfolio.balance || 500;

  console.log(`Starting balance: €${initialBalance.toFixed(2)}`);

  // Simulate buying and selling to test the logic
  console.log('\n📝 Instructions for manual testing:');
  console.log('1. Note your current balance');
  console.log('2. Buy some crypto (e.g., 1 unit of XRP)');
  console.log('3. Check that balance decreased by (price + fee)');
  console.log('4. Sell the same amount');
  console.log('5. Check that balance increased by (price - fee)');
  console.log('6. Run checkPortfolio() to verify calculations');

  return { initialBalance };
};

// Function to monitor trades in real-time
window.startTradeMonitoring = function() {
  console.log('\n👀 Starting trade monitoring...');

  let lastTradeCount = 0;

  const monitor = setInterval(() => {
    const trades = JSON.parse(localStorage.getItem('cts_trades_v1') || '[]');

    if (trades.length > lastTradeCount) {
      const newTrades = trades.slice(0, trades.length - lastTradeCount);
      newTrades.forEach(trade => {
        console.log(`\n🔔 New ${trade.type} trade detected:`);
        console.log(`Symbol: ${trade.symbol}`);
        console.log(`Amount: ${trade.amount.toFixed(6)}`);
        console.log(`Price: €${trade.price.toFixed(3)}`);
        console.log(`Total: €${trade.total.toFixed(3)}`);
        console.log(`Fee: €${trade.fee.toFixed(3)} (${((trade.fee/trade.total)*100).toFixed(2)}%)`);

        if (trade.profit !== undefined) {
          console.log(`Profit: €${trade.profit.toFixed(3)}`);
        }

        // Verify the trade
        verifyTrade(trade);
      });

      lastTradeCount = trades.length;
    }
  }, 1000);

  console.log('Trade monitoring started. Use stopTradeMonitoring() to stop.');
  window.tradeMonitorInterval = monitor;
};

window.stopTradeMonitoring = function() {
  if (window.tradeMonitorInterval) {
    clearInterval(window.tradeMonitorInterval);
    console.log('Trade monitoring stopped.');
  }
};

console.log('\n✅ Test functions loaded:');
console.log('- verifyTrade(trade): Verify a specific trade');
console.log('- checkPortfolio(): Check portfolio consistency');
console.log('- testTradingCycle(): Get testing instructions');
console.log('- startTradeMonitoring(): Monitor trades in real-time');
console.log('- stopTradeMonitoring(): Stop monitoring');
