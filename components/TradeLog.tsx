
import React from 'react';
import { Trade } from '../types';
import { ArrowUpRight, ArrowDownLeft } from 'lucide-react';

const TradeLog: React.FC<{ trades: Trade[] }> = ({ trades }) => {
  const fmtTime = (dt: Date) =>
    new Date(dt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

  return (
    <div className="bg-gradient-to-br from-gray-800/70 to-gray-900/70 rounded-2xl p-5 border border-gray-700/30 backdrop-blur-sm">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-semibold text-gray-200">Recente Trades</h3>
        <span className="text-xs text-gray-500">{trades.length} transacties</span>
      </div>
      <div className="overflow-y-auto max-h-80 space-y-3">
        {trades.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <div className="text-sm">Nog geen transacties</div>
            <div className="text-xs text-gray-600 mt-1">Trades verschijnen hier automatisch</div>
          </div>
        ) : (
          trades.map((trade) => {
            const isBuy = trade.type === 'BUY';
            return (
              <div
                key={trade.id}
                className="bg-gray-900/40 border border-gray-700/20 p-3 rounded-xl hover:bg-gray-900/60 transition-colors"
              >
                <div className="flex justify-between items-start">
                  <div className="flex items-center gap-3">
                    <div className={`p-1.5 rounded-lg ${isBuy ? 'bg-green-500/10' : 'bg-red-500/10'}`}>
                      {isBuy ? (
                        <ArrowDownLeft className="text-green-400 h-4 w-4" />
                      ) : (
                        <ArrowUpRight className="text-red-400 h-4 w-4" />
                      )}
                    </div>
                    <div>
                      <div className="font-semibold text-white text-sm">
                        <span className={isBuy ? 'text-green-400' : 'text-red-400'}>{trade.type}</span>{' '}
                        {trade.symbol.replace('-EUR', '')}
                      </div>
                      <div className="text-xs text-gray-500 mt-0.5">{fmtTime(trade.timestamp)}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-white text-sm font-mono">
                      {trade.amount.toFixed(4)} @ €{trade.price.toFixed(3)}
                    </div>
                    {trade.profit !== undefined && (
                      <div className={`text-xs font-semibold mt-0.5 ${trade.profit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {trade.profit >= 0 ? '+' : ''}€{trade.profit.toFixed(2)}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
};

export default TradeLog;