import React, { useState, useEffect } from 'react';
import { Clock, Brain } from 'lucide-react';

interface AIUpdateTimerProps {
  lastAdviceAt: Record<string, number>;
  intervalMs: number;
  symbols: string[];
}

const AIUpdateTimer: React.FC<AIUpdateTimerProps> = ({ lastAdviceAt, intervalMs, symbols }) => {
  const [currentTime, setCurrentTime] = useState(Date.now());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const getTimeUntilNext = (symbol: string) => {
    const lastUpdate = lastAdviceAt[symbol] || 0;
    const nextUpdate = lastUpdate + intervalMs;
    const timeLeft = Math.max(0, nextUpdate - currentTime);
    return timeLeft;
  };

  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    }
    return `${remainingSeconds}s`;
  };

  const getNextSymbol = () => {
    return symbols.reduce((next, symbol) => {
      const timeLeft = getTimeUntilNext(symbol);
      const nextTimeLeft = getTimeUntilNext(next);
      return timeLeft < nextTimeLeft ? symbol : next;
    }, symbols[0]);
  };

  const nextSymbol = getNextSymbol();
  const nextUpdateTime = getTimeUntilNext(nextSymbol);
  const isUpdating = nextUpdateTime <= 1000; // Within 1 second

  return (
    <div className="bg-gradient-to-br from-blue-800/20 to-purple-800/20 rounded-xl p-4 border border-blue-700/30">
      <div className="flex items-center gap-2 mb-3">
        <Brain className={`text-blue-400 ${isUpdating ? 'animate-pulse' : ''}`} size={18} />
        <h3 className="text-sm font-semibold text-blue-200">AI Update Status</h3>
      </div>

      <div className="space-y-3">
        {/* Next Update */}
        <div className="bg-blue-900/30 border border-blue-700/20 p-3 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Clock size={14} className="text-blue-400" />
              <span className="text-xs text-blue-300">Volgende update:</span>
            </div>
            <div className="text-right">
              <div className="text-sm font-mono text-white">
                {nextSymbol.replace('-EUR', '')}
              </div>
              <div className={`text-xs font-semibold ${isUpdating ? 'text-green-400 animate-pulse' : 'text-blue-400'}`}>
                {isUpdating ? 'Updating...' : formatTime(nextUpdateTime)}
              </div>
            </div>
          </div>
        </div>

        {/* All Symbols Status */}
        <div className="space-y-2">
          <div className="text-xs text-gray-400">Status per symbool:</div>
          {symbols.map(symbol => {
            const timeLeft = getTimeUntilNext(symbol);
            const isActive = timeLeft <= 1000;
            const lastUpdate = lastAdviceAt[symbol];
            
            return (
              <div key={symbol} className="flex items-center justify-between text-xs">
                <span className="text-gray-300">{symbol.replace('-EUR', '')}</span>
                <div className="flex items-center gap-2">
                  {lastUpdate && (
                    <span className="text-gray-500">
                      {new Date(lastUpdate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </span>
                  )}
                  <span className={`font-mono ${isActive ? 'text-green-400 animate-pulse' : 'text-blue-400'}`}>
                    {isActive ? 'Nu' : formatTime(timeLeft)}
                  </span>
                </div>
              </div>
            );
          })}
        </div>

        {/* Interval Info */}
        <div className="border-t border-blue-700/20 pt-2">
          <div className="text-xs text-gray-400">
            Update interval: {Math.round(intervalMs / 60000)} minuten
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIUpdateTimer;
