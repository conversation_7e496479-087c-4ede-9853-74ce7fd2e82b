
import React from 'react';
import { Portfolio } from '../types';
import { Wallet, Landmark } from 'lucide-react';

const PortfolioPanel: React.FC<{ portfolio: Portfolio }> = ({ portfolio }) => {
  const { totalValue, pl, plPercent, balance, holdings, initialValue } = portfolio;
  const isProfit = pl >= 0;

  const StatCard: React.FC<{ icon: React.ReactNode, label: string, value: string, valueColor?: string, className?: string }> = ({ icon, label, value, valueColor, className }) => (
    <div className={`flex items-center gap-4 p-3 rounded-lg bg-gray-700/50 ${className}`}>
      <div className="text-teal-400">{icon}</div>
      <div>
        <div className="text-xs text-gray-400">{label}</div>
        <div className={`text-sm font-bold ${valueColor}`}>{value}</div>
      </div>
    </div>
  );

  return (
    <div className="bg-gradient-to-br from-gray-800/70 to-gray-900/70 rounded-2xl p-6 border border-gray-700/30 space-y-6 backdrop-blur-sm">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-bold text-white">Portfolio Details</h2>
        <div className={`px-3 py-1 rounded-full text-xs font-semibold ${isProfit ? 'bg-green-500/10 text-green-400' : 'bg-red-500/10 text-red-400'}`}>
          {isProfit ? '↗ Winst' : '↘ Verlies'}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <StatCard
          icon={<Landmark size={20}/>}
          label="Startkapitaal"
          value={`€${initialValue.toFixed(2)}`}
          className="bg-gray-900/50 border border-gray-700/30"
        />
        <StatCard
          icon={<Wallet size={20}/>}
          label="Beschikbaar Saldo"
          value={`€${balance.toFixed(2)}`}
          valueColor="text-white"
          className="bg-gray-900/50 border border-gray-700/30"
        />
      </div>

      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-sm font-semibold text-gray-300">Crypto Holdings</h3>
          <span className="text-xs text-gray-500">{holdings.length} activa</span>
        </div>
        <div className="space-y-3">
          {holdings.length > 0 ? holdings.map(holding => (
            <div key={holding.symbol} className="bg-gray-900/40 border border-gray-700/20 p-4 rounded-xl flex items-center justify-between hover:bg-gray-900/60 transition-colors">
              <div>
                <div className="font-semibold text-white text-base">{holding.symbol.replace('-EUR', '')}</div>
                <div className="text-xs text-gray-400 mt-1">{holding.amount.toFixed(6)} coins</div>
              </div>
              <div className="text-right">
                <div className="font-mono text-white text-lg font-bold">€{holding.value.toFixed(2)}</div>
                <div className="text-xs text-gray-400">waarde</div>
              </div>
            </div>
          )) : (
            <div className="text-center text-gray-500 py-8 bg-gray-900/30 rounded-xl border border-gray-700/20">
              <Wallet className="mx-auto h-8 w-8 text-gray-600 mb-2" />
              <p className="text-sm">Geen crypto activa in bezit</p>
              <p className="text-xs text-gray-600 mt-1">Start met handelen om je portfolio op te bouwen</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PortfolioPanel;