import React, { useState } from 'react';
import { Asset, Portfolio, Trade } from '../types';
import { Calculator, DollarSign, TrendingUp, TrendingDown } from 'lucide-react';
import { TRADING_FEE_PERCENT } from '../constants';

interface TradingDebugProps {
  assets: Asset[];
  portfolio: Portfolio;
  trades: Trade[];
  onTrade: (asset: Asset, type: 'BUY' | 'SELL', amount: number) => void;
}

const TradingDebug: React.FC<TradingDebugProps> = ({ assets, portfolio, trades, onTrade }) => {
  const [selectedAsset, setSelectedAsset] = useState<string>('');
  const [tradeAmount, setTradeAmount] = useState<string>('');
  const [tradeType, setTradeType] = useState<'BUY' | 'SELL'>('BUY');

  const asset = assets.find(a => a.symbol === selectedAsset);
  const holding = portfolio.holdings.find(h => h.symbol === selectedAsset);
  
  // Calculate trade preview
  const amount = parseFloat(tradeAmount) || 0;
  const price = asset?.price || 0;
  const total = amount * price;
  const fee = total * TRADING_FEE_PERCENT;
  const totalCost = tradeType === 'BUY' ? total + fee : total - fee;

  // Calculate total fees paid
  const totalFeesPaid = trades.reduce((sum, trade) => sum + trade.fee, 0);

  const handleTrade = () => {
    if (!asset || amount <= 0) return;
    
    if (tradeType === 'BUY' && portfolio.balance < total + fee) {
      alert('Onvoldoende saldo!');
      return;
    }
    
    if (tradeType === 'SELL' && (!holding || holding.amount < amount)) {
      alert('Onvoldoende crypto in bezit!');
      return;
    }
    
    onTrade(asset, tradeType, amount);
    setTradeAmount('');
  };

  return (
    <div className="bg-gradient-to-br from-gray-800/70 to-gray-900/70 rounded-2xl p-5 border border-gray-700/30 backdrop-blur-sm">
      <div className="flex items-center gap-2 mb-4">
        <Calculator className="text-blue-400" size={20} />
        <h3 className="text-sm font-semibold text-gray-200">Trading Debug</h3>
      </div>

      {/* Fee Summary */}
      <div className="bg-gray-900/40 border border-gray-700/20 p-3 rounded-xl mb-4">
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-400">Totale fees betaald:</span>
          <span className="text-sm font-mono text-orange-400">€{totalFeesPaid.toFixed(3)}</span>
        </div>
        <div className="flex items-center justify-between mt-1">
          <span className="text-xs text-gray-400">Fee percentage:</span>
          <span className="text-sm font-mono text-gray-300">{(TRADING_FEE_PERCENT * 100).toFixed(2)}%</span>
        </div>
      </div>

      {/* Manual Trade Form */}
      <div className="space-y-3">
        <div>
          <label className="block text-xs text-gray-400 mb-1">Asset</label>
          <select 
            value={selectedAsset} 
            onChange={(e) => setSelectedAsset(e.target.value)}
            className="w-full bg-gray-900/50 border border-gray-700/30 rounded-lg px-3 py-2 text-white text-sm"
          >
            <option value="">Selecteer asset...</option>
            {assets.filter(a => a.symbol.includes('-EUR')).map(asset => (
              <option key={asset.symbol} value={asset.symbol}>
                {asset.name} (€{asset.price.toFixed(3)})
              </option>
            ))}
          </select>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <div>
            <label className="block text-xs text-gray-400 mb-1">Type</label>
            <div className="flex gap-1">
              <button
                onClick={() => setTradeType('BUY')}
                className={`flex-1 px-3 py-2 rounded-lg text-xs font-semibold transition-colors ${
                  tradeType === 'BUY' 
                    ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
                    : 'bg-gray-700/50 text-gray-400 border border-gray-700/30'
                }`}
              >
                <TrendingUp size={14} className="inline mr-1" />
                BUY
              </button>
              <button
                onClick={() => setTradeType('SELL')}
                className={`flex-1 px-3 py-2 rounded-lg text-xs font-semibold transition-colors ${
                  tradeType === 'SELL' 
                    ? 'bg-red-500/20 text-red-400 border border-red-500/30' 
                    : 'bg-gray-700/50 text-gray-400 border border-gray-700/30'
                }`}
              >
                <TrendingDown size={14} className="inline mr-1" />
                SELL
              </button>
            </div>
          </div>

          <div>
            <label className="block text-xs text-gray-400 mb-1">Hoeveelheid</label>
            <input
              type="number"
              value={tradeAmount}
              onChange={(e) => setTradeAmount(e.target.value)}
              placeholder="0.0"
              step="0.000001"
              className="w-full bg-gray-900/50 border border-gray-700/30 rounded-lg px-3 py-2 text-white text-sm"
            />
          </div>
        </div>

        {/* Trade Preview */}
        {asset && amount > 0 && (
          <div className="bg-gray-900/40 border border-gray-700/20 p-3 rounded-xl">
            <div className="text-xs text-gray-400 mb-2">Preview:</div>
            <div className="space-y-1 text-xs">
              <div className="flex justify-between">
                <span className="text-gray-400">Prijs per eenheid:</span>
                <span className="text-white font-mono">€{price.toFixed(3)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Subtotaal:</span>
                <span className="text-white font-mono">€{total.toFixed(3)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Fee (0.25%):</span>
                <span className="text-orange-400 font-mono">€{fee.toFixed(3)}</span>
              </div>
              <div className="flex justify-between border-t border-gray-700/30 pt-1">
                <span className="text-gray-300 font-semibold">
                  {tradeType === 'BUY' ? 'Totale kosten:' : 'Netto ontvangen:'}
                </span>
                <span className={`font-mono font-semibold ${tradeType === 'BUY' ? 'text-red-400' : 'text-green-400'}`}>
                  €{Math.abs(totalCost).toFixed(3)}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Current Holdings */}
        {selectedAsset && holding && (
          <div className="bg-blue-500/10 border border-blue-500/20 p-3 rounded-xl">
            <div className="text-xs text-blue-400 mb-1">Huidige positie:</div>
            <div className="text-sm text-white font-mono">
              {holding.amount.toFixed(6)} {selectedAsset.replace('-EUR', '')} 
              <span className="text-gray-400 ml-2">(€{holding.value.toFixed(2)})</span>
            </div>
          </div>
        )}

        <button
          onClick={handleTrade}
          disabled={!asset || amount <= 0}
          className="w-full bg-blue-500/20 hover:bg-blue-500/30 disabled:bg-gray-700/30 disabled:text-gray-500 text-blue-400 border border-blue-500/30 disabled:border-gray-700/30 rounded-lg px-4 py-2 text-sm font-semibold transition-colors"
        >
          <DollarSign size={16} className="inline mr-2" />
          Uitvoeren {tradeType}
        </button>
      </div>
    </div>
  );
};

export default TradingDebug;
