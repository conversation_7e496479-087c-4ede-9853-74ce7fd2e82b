import React, { useState, useEffect, useCallback } from 'react';
import { useCryptoSimulator } from './hooks/useCryptoSimulator';
import { getTradingAdvice } from './services/aiService';
import { Prediction, AppState } from './types';
import Header from './components/Header';
import PortfolioPanel from './components/PortfolioPanel';
import TradeLog from './components/TradeLog';
import MarketIndicators from './components/MarketIndicators';
import AssetSummaryCard from './components/AssetSummaryCard';
import { TRADABLE_ASSET_SYMBOLS } from './constants';
import AnalysisMonitor from './components/AnalysisMonitor';
import AIUpdateTimer from './components/AIUpdateTimer';


const STORAGE_VERSION = 1;
const LS = {
  predictions: 'cts_predictions_v' + STORAGE_VERSION,
  analysisStates: 'cts_analysis_states_v' + STORAGE_VERSION,
  lastErrors: 'cts_last_errors_v' + STORAGE_VERSION,
  analysisQueue: 'cts_analysis_queue_v' + STORAGE_VERSION,
  autoTradeEnabled: 'cts_auto_trade_enabled_v' + STORAGE_VERSION,
  lastActions: 'cts_last_actions_v' + STORAGE_VERSION,
  lastAdviceAt: 'cts_last_advice_at_v' + STORAGE_VERSION,
  profile: 'cts_profile_v' + STORAGE_VERSION,
};

// Auto-trading configuration (more active defaults)
const DEFAULT_MIN_EUR_TRADE = 5;
const DEFAULT_CONFIDENCE_THRESHOLD = 0.5;
const DEFAULT_SYMBOL_COOLDOWN_MS = 3 * 60 * 1000; // 3 minutes
// AI advice cadence (per symbol) - updated to 2 minutes
const ADVICE_INTERVAL_MS = 2 * 60 * 1000; // 2 minutes (120 seconds)


type LastAction = {
  last: 'BUY' | 'SELL' | null;
  time: number; // epoch ms
  price: number | null;
};

function loadJSON<T>(key: string, fallback: T): T {
  try {
    const raw = localStorage.getItem(key);
    if (!raw) return fallback;
    return JSON.parse(raw) as T;
  } catch {
    return fallback;
  }
}

const App: React.FC = () => {
  const { assets, portfolio, tradeLog, simulateTrade, resetSimulation, candleHistories1m, candleHistories1h } = useCryptoSimulator();

  const [analysisStates, setAnalysisStates] = useState<Record<string, AppState>>(() =>
    loadJSON<Record<string, AppState>>(LS.analysisStates, Object.fromEntries(TRADABLE_ASSET_SYMBOLS.map(s => [s, AppState.Monitoring])))
  );
  const [predictions, setPredictions] = useState<Record<string, Prediction | null>>(() =>
    loadJSON<Record<string, Prediction | null>>(LS.predictions, {})
  );
  const [lastErrors, setLastErrors] = useState<Record<string, string | null>>(() =>
    loadJSON<Record<string, string | null>>(LS.lastErrors, {})
  );

  // Auto-trading toggles and memory
  const [autoTradeEnabled, setAutoTradeEnabled] = useState<boolean>(() =>
    loadJSON<boolean>(LS.autoTradeEnabled, true)
  );
  const [lastActions, setLastActions] = useState<Record<string, LastAction>>(() =>
    loadJSON<Record<string, LastAction>>(LS.lastActions, {})
  );
  // Last advice timestamps per symbol + simple profiles
  const [lastAdviceAt, setLastAdviceAt] = useState<Record<string, number>>(() => loadJSON<Record<string, number>>(LS.lastAdviceAt, {}));
  const [profile, setProfile] = useState<'conservative'|'balanced'|'aggressive'>(() => loadJSON(LS.profile, 'aggressive'));


  // New state for managing a sequential analysis queue to avoid rate limiting
  const [analysisQueue, setAnalysisQueue] = useState<string[]>(() =>
    loadJSON<string[]>(LS.analysisQueue, [])
  );

  // User-tunable settings (persisted)
  const [minEurTrade, setMinEurTrade] = useState<number>(() => loadJSON<number>(LS.autoTradeEnabled + '_min', DEFAULT_MIN_EUR_TRADE));
  const [confidenceThreshold, setConfidenceThreshold] = useState<number>(() => loadJSON<number>(LS.autoTradeEnabled + '_conf', DEFAULT_CONFIDENCE_THRESHOLD));
  const [symbolCooldownMs, setSymbolCooldownMs] = useState<number>(() => loadJSON<number>(LS.autoTradeEnabled + '_cooldown', DEFAULT_SYMBOL_COOLDOWN_MS));


  // Persist app-level state so the app resumes after refresh/reopen
  useEffect(() => {
    try {
      localStorage.setItem(LS.predictions, JSON.stringify(predictions));
      localStorage.setItem(LS.autoTradeEnabled + '_min', JSON.stringify(minEurTrade));
      localStorage.setItem(LS.autoTradeEnabled + '_conf', JSON.stringify(confidenceThreshold));
      localStorage.setItem(LS.autoTradeEnabled + '_cooldown', JSON.stringify(symbolCooldownMs));
      localStorage.setItem(LS.analysisStates, JSON.stringify(analysisStates));
      localStorage.setItem(LS.lastErrors, JSON.stringify(lastErrors));
      localStorage.setItem(LS.analysisQueue, JSON.stringify(analysisQueue));
      localStorage.setItem(LS.autoTradeEnabled, JSON.stringify(autoTradeEnabled));
      localStorage.setItem(LS.lastActions, JSON.stringify(lastActions));
      localStorage.setItem(LS.lastAdviceAt, JSON.stringify(lastAdviceAt));
      localStorage.setItem(LS.profile, JSON.stringify(profile));
    } catch {
      // ignore storage errors
    }
  }, [predictions, analysisStates, lastErrors, analysisQueue, autoTradeEnabled, lastActions, lastAdviceAt, profile]);

  // Reset simulation: clears portfolio and tradeLog; preserves candles/predictions ("geleerd" context)
  const onReset = useCallback(() => {
    const ok = window.confirm('Reset simulatie? Portfolio en transactielog worden gewist, analyses en candles blijven bewaard.');
    if (!ok) return;
    resetSimulation();
    setLastErrors({});
    // Clear predictions so AI bouwt opnieuw op
    setPredictions({});
    // Normalize analysis state so symbols continue monitoring instead of getting stuck
    setAnalysisStates(prev => {
      const next = { ...prev };
      TRADABLE_ASSET_SYMBOLS.forEach(s => { next[s] = AppState.Monitoring; });
      return next;
    });
  }, [resetSimulation]);



  // Create a stable handleAnalysis function
  const handleAnalysis = useCallback(async (symbol: string) => {
    const currentHistories1m = candleHistories1m[symbol] ?? [];
    const currentHistories1h = candleHistories1h[symbol] ?? [];

    console.log(`[AutoTrade] ${symbol} - 1m candles: ${currentHistories1m.length}, 1h candles: ${currentHistories1h.length}, state: ${analysisStates[symbol]}`);

    // Check if we have sufficient data
    if (currentHistories1m.length < 10 || currentHistories1h.length < 3) {
      console.log(`[AutoTrade] ${symbol} - Skipping analysis: insufficient data (1m: ${currentHistories1m.length}, 1h: ${currentHistories1h.length})`);
      return;
    }

    // Check if already analyzing (but allow retry after timeout)
    if (analysisStates[symbol] === AppState.Analyzing) {
      console.log(`[AutoTrade] ${symbol} - Already analyzing, skipping...`);
      return;
    }

    setAnalysisStates(prev => ({ ...prev, [symbol]: AppState.Analyzing }));
    setLastErrors(prev => ({ ...prev, [symbol]: null }));

    try {
      console.log(`[AutoTrade] ${symbol} - Starting AI analysis...`);
      const advice = await getTradingAdvice(symbol, currentHistories1m, currentHistories1h, tradeLog, portfolio);
      console.log(`[AutoTrade] ${symbol} - AI advice received:`, advice.prediction, 'confidence:', advice.confidence);

      setPredictions(prev => ({ ...prev, [symbol]: advice }));
      setLastAdviceAt(prev => ({ ...prev, [symbol]: Date.now() }));

      const assetToTrade = assets.find(a => a.symbol === symbol);

      // Execute autonomous trade only if enabled and asset is known
      console.log(`[AutoTrade] ${symbol} - autoTradeEnabled: ${autoTradeEnabled}, assetFound: ${!!assetToTrade}, advice: ${advice.prediction}, confidence: ${advice.confidence}`);
      if (autoTradeEnabled && assetToTrade) {
        const conf = advice.confidence ?? 0;
        const now = Date.now();
        const la = lastActions[symbol] ?? { last: null, time: 0, price: null };

        // Helper to update last action state
        const commitLastAction = (typ: 'BUY' | 'SELL', price: number | null) => {
          setLastActions(prev => ({
            ...prev,
            [symbol]: { last: typ, time: now, price }
          }));
        };

        // Cooldown check
        const underCooldown = now - (la.time ?? 0) < symbolCooldownMs;
        console.log(`[AutoTrade] ${symbol} advice=${advice.prediction} conf=${conf.toFixed(2)} cooldown=${underCooldown} last=${la.last} price=${assetToTrade.price}`);

        // BUY path
        if (advice.prediction === 'BUY') {
          if (!underCooldown || la.last !== 'BUY') {
            if (conf >= confidenceThreshold) {
              const pct = Math.max(1, Math.min(100, advice.positionSizing?.pctOfPortfolio ?? 25));
              const eurToSpend = Math.min(portfolio.balance, portfolio.balance * (pct / 100));
              if (assetToTrade.price > 0 && eurToSpend >= minEurTrade) {
                console.log(`[AutoTrade] BUY ${symbol} eur=${eurToSpend.toFixed(2)} @ ${assetToTrade.price}`);
                simulateTrade(assetToTrade, 'BUY', eurToSpend / assetToTrade.price);
                commitLastAction('BUY', assetToTrade.price);
              } else {
                console.log(`[AutoTrade] BUY skipped for ${symbol}: price=${assetToTrade.price} eurToSpend=${eurToSpend.toFixed(2)} (min=${minEurTrade})`);
              }
            } else {
              console.log(`[AutoTrade] BUY skipped for ${symbol}: confidence ${conf.toFixed(2)} < threshold ${confidenceThreshold}`);
            }
          } else {
            console.log(`[AutoTrade] BUY skipped for ${symbol}: under cooldown and last was BUY`);
          }
        }

        // SELL path
        if (advice.prediction === 'SELL') {
          if (!underCooldown || la.last !== 'SELL') {
            if (conf >= confidenceThreshold) {
              const assetBalance = portfolio.holdings.find(h => h.symbol === symbol)?.amount ?? 0;
              const price = assetToTrade.price;
              const sellAmount = assetBalance * 0.75; // conservative partial exit
              if (price > 0 && sellAmount > 0 && (sellAmount * price) >= minEurTrade) {
                console.log(`[AutoTrade] SELL ${symbol} amount=${sellAmount.toFixed(6)} ~€${(sellAmount*price).toFixed(2)} @ ${price}`);
                simulateTrade(assetToTrade, 'SELL', sellAmount);
                commitLastAction('SELL', price);
              } else {
                console.log(`[AutoTrade] SELL skipped for ${symbol}: price=${price} amount=${sellAmount.toFixed(6)} (value ~€${(sellAmount*price).toFixed(2)})`);
              }
            } else {
              console.log(`[AutoTrade] SELL skipped for ${symbol}: confidence ${conf.toFixed(2)} < threshold ${confidenceThreshold}`);
            }
          } else {
            console.log(`[AutoTrade] SELL skipped for ${symbol}: under cooldown and last was SELL`);
          }
        }
      }

      setAnalysisStates(prev => ({ ...prev, [symbol]: AppState.Monitoring }));
    } catch (error) {
      console.error(`Error getting trading advice for ${symbol}:`, error);
      const errorMessage = error instanceof Error ? error.message : "An unknown error occurred.";

      if (errorMessage.includes('429') || errorMessage.includes('RESOURCE_EXHAUSTED') || errorMessage.includes('quota')) {
        setLastErrors(prev => ({ ...prev, [symbol]: `API-limiet bereikt. Volgende poging in cyclus.` }));
      } else {
        setLastErrors(prev => ({ ...prev, [symbol]: `Analyse mislukt.` }));
      }
      setAnalysisStates(prev => ({ ...prev, [symbol]: AppState.Error }));
    }
  }, [candleHistories1m, candleHistories1h, analysisStates, tradeLog, portfolio, assets, autoTradeEnabled, lastActions, confidenceThreshold, minEurTrade, symbolCooldownMs, simulateTrade]);

  // Switch to per-symbol interval cadence (30s) – disable queue population
  useEffect(() => {
    setAnalysisQueue([]);
  }, []);

  // Reset any stuck analyzing states on mount
  useEffect(() => {
    const currentStates = { ...analysisStates };
    let hasStuckStates = false;

    TRADABLE_ASSET_SYMBOLS.forEach(symbol => {
      if (currentStates[symbol] === AppState.Analyzing) {
        currentStates[symbol] = AppState.Monitoring;
        hasStuckStates = true;
      }
    });

    if (hasStuckStates) {
      console.log('[AutoTrade] Resetting stuck analyzing states...');
      setAnalysisStates(currentStates);
    }
  }, []);

  // Per-symbol 2-minute cadence: trigger analysis every ADVICE_INTERVAL_MS per tradable symbol
  useEffect(() => {
    console.log(`[AutoTrade] Setting up analysis intervals (${ADVICE_INTERVAL_MS/60000} minutes per symbol)...`);

    const timers = TRADABLE_ASSET_SYMBOLS.map((symbol, index) => {
      const tick = () => {
        console.log(`[AutoTrade] Triggering analysis for ${symbol} at ${new Date().toLocaleTimeString()} (next in ${ADVICE_INTERVAL_MS/60000}m)`);
        handleAnalysis(symbol);
      };
      // Trigger immediately on mount with a small delay to spread the load
      setTimeout(tick, index * 1000);
      return setInterval(tick, ADVICE_INTERVAL_MS);
    });

    return () => {
      console.log('[AutoTrade] Cleaning up analysis intervals');
      timers.forEach(clearInterval);
    };
  }, [handleAnalysis]);


  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-900 to-gray-800 text-gray-100 font-sans">
      <Header />
      <main className="container mx-auto px-4 lg:px-6 py-8 space-y-8">
        {/* Portfolio Overview - Hero Section */}
        <section className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 rounded-2xl p-6 border border-gray-700/30 backdrop-blur-sm">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <div className="lg:col-span-2">
              <div className="text-sm text-gray-400 mb-2">Portfolio Waarde</div>
              <div className="text-4xl lg:text-5xl font-extrabold text-white tracking-tight mb-2">
                €{portfolio.totalValue.toFixed(2)}
              </div>
              <div className={`text-lg font-semibold flex items-center gap-2 ${portfolio.pl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {portfolio.pl >= 0 ? '↗' : '↘'} {portfolio.pl >= 0 ? '+' : ''}€{portfolio.pl.toFixed(2)} ({portfolio.plPercent.toFixed(2)}%)
              </div>
            </div>
            <div className="grid grid-cols-2 lg:grid-cols-1 gap-4">
              <div className="bg-gray-900/40 rounded-xl p-4 border border-gray-700/20">
                <div className="text-xs text-gray-400 mb-1">Beschikbaar Saldo</div>
                <div className="text-xl font-bold text-white">€{portfolio.balance.toFixed(2)}</div>
              </div>
              <div className="bg-gray-900/40 rounded-xl p-4 border border-gray-700/20">
                <div className="text-xs text-gray-400 mb-1">Activa in Bezit</div>
                <div className="text-xl font-bold text-white">{portfolio.holdings.length}</div>
              </div>
            </div>
            <div className="bg-gray-900/40 rounded-xl p-4 border border-gray-700/20">
              <div className="text-xs text-gray-400 mb-1">Totaal Trades</div>
              <div className="text-xl font-bold text-white">{tradeLog.length}</div>
              <div className="text-xs text-gray-500 mt-1">
                {tradeLog.filter(t => t.type === 'BUY').length} BUY • {tradeLog.filter(t => t.type === 'SELL').length} SELL
              </div>
            </div>
          </div>
        </section>

        {/* AI Trading Controls */}
        <section className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* AI Status */}
          <div className="bg-gray-800/60 rounded-xl p-5 border border-gray-700/30">
            <div className="text-sm font-semibold text-gray-200 mb-3">AI Trading Status</div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-400">DeepSeek API</span>
                <span className={String(((import.meta as any).env?.VITE_DEEPSEEK_API_KEY) ? 'text-green-400' : 'text-red-400')}>
                  {((import.meta as any).env?.VITE_DEEPSEEK_API_KEY) ? '✓ Actief' : '✗ Ontbreekt'}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-400">Autonoom Handelen</span>
                <span className={autoTradeEnabled ? 'text-green-400' : 'text-gray-400'}>
                  {autoTradeEnabled ? '✓ Aan' : '○ Uit'}
                </span>
              </div>
              {Object.values(lastAdviceAt).length > 0 && (() => {
                const times = Object.values(lastAdviceAt) as number[];
                const latest = Math.max(...times);
                return (
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-400">Laatste Advies</span>
                    <span className="text-xs text-gray-300">{new Date(latest).toLocaleTimeString()}</span>
                  </div>
                );
              })()}
              <div className="pt-2">
                <button
                  onClick={() => setAutoTradeEnabled(v => !v)}
                  className={`w-full px-4 py-2 rounded-lg text-sm font-semibold border transition
                    ${autoTradeEnabled ? 'bg-red-500/10 border-red-500/40 text-red-300 hover:bg-red-500/20' : 'bg-green-500/10 border-green-500/40 text-green-300 hover:bg-green-500/20'}`}
                >
                  {autoTradeEnabled ? 'Uitschakelen' : 'Inschakelen'}
                </button>
              </div>
            </div>
          </div>

          {/* Trading Settings */}
          <div className="bg-gray-800/60 rounded-xl p-5 border border-gray-700/30">
            <div className="text-sm font-semibold text-gray-200 mb-3">Trading Instellingen</div>
            <div className="space-y-3">
              <label className="block">
                <span className="text-xs text-gray-400 mb-1 block">Risico Profiel</span>
                <select
                  value={profile}
                  onChange={(e) => {
                    const p = e.target.value as 'conservative'|'balanced'|'aggressive';
                    setProfile(p);
                    if (p === 'conservative') {
                      setConfidenceThreshold(0.65);
                      setSymbolCooldownMs(10 * 60000);
                      setMinEurTrade(15);
                    } else if (p === 'balanced') {
                      setConfidenceThreshold(0.55);
                      setSymbolCooldownMs(8 * 60000);
                      setMinEurTrade(10);
                    } else {
                      setConfidenceThreshold(0.5);
                      setSymbolCooldownMs(5 * 60000);
                      setMinEurTrade(10);
                    }
                  }}
                  className="w-full bg-gray-900 border border-gray-700 rounded-lg px-3 py-2 text-gray-200 text-sm"
                >
                  <option value="conservative">🛡️ Conservative</option>
                  <option value="balanced">⚖️ Balanced</option>
                  <option value="aggressive">🚀 Aggressive</option>
                </select>
              </label>

              <div className="grid grid-cols-2 gap-3">
                <label className="block">
                  <span className="text-xs text-gray-400 mb-1 block">Min €/trade</span>
                  <input
                    type="number"
                    min={1}
                    step={1}
                    value={minEurTrade}
                    onChange={e => setMinEurTrade(Math.max(1, Number(e.target.value) || DEFAULT_MIN_EUR_TRADE))}
                    className="w-full bg-gray-900 border border-gray-700 rounded-lg px-3 py-2 text-gray-200 text-sm"
                  />
                </label>
                <label className="block">
                  <span className="text-xs text-gray-400 mb-1 block">Confidence</span>
                  <input
                    type="number"
                    min={0}
                    max={1}
                    step={0.01}
                    value={confidenceThreshold}
                    onChange={e => setConfidenceThreshold(Math.min(1, Math.max(0, Number(e.target.value))))}
                    className="w-full bg-gray-900 border border-gray-700 rounded-lg px-3 py-2 text-gray-200 text-sm"
                  />
                </label>
              </div>

              <label className="block">
                <span className="text-xs text-gray-400 mb-1 block">Cooldown (minuten)</span>
                <input
                  type="number"
                  min={0}
                  step={1}
                  value={Math.round(symbolCooldownMs/60000)}
                  onChange={e => setSymbolCooldownMs(Math.max(0, Number(e.target.value) * 60000))}
                  className="w-full bg-gray-900 border border-gray-700 rounded-lg px-3 py-2 text-gray-200 text-sm"
                />
              </label>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-gray-800/60 rounded-xl p-5 border border-gray-700/30">
            <div className="text-sm font-semibold text-gray-200 mb-3">Acties</div>
            <div className="space-y-3">
              <button
                onClick={onReset}
                className="w-full px-4 py-2 rounded-lg text-sm font-semibold border transition bg-gray-700/50 border-gray-600 text-gray-200 hover:bg-gray-700"
              >
                🔄 Reset Simulatie
              </button>
              <div className="text-xs text-gray-500 space-y-1">
                <div>• AI Interval: {Math.round(ADVICE_INTERVAL_MS/60000)}m per symbool</div>
                <div>• Cooldown: {Math.round(symbolCooldownMs/60000)}m</div>
                <div>• Min trade: €{minEurTrade}</div>
              </div>
            </div>
          </div>
        </section>



        {/* Crypto Assets Overview */}
        <section>
          <div className="mb-4">
            <h2 className="text-xl font-bold text-white">Crypto Assets</h2>
            <p className="text-sm text-gray-400">Live prijzen en AI trading adviezen</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
            {TRADABLE_ASSET_SYMBOLS.map(symbol => {
              const isDataSufficient = (candleHistories1m[symbol]?.length ?? 0) >= 10 && (candleHistories1h[symbol]?.length ?? 0) >= 3;
              const price = assets.find(a => a.symbol === symbol)?.price ?? 0;
              return (
                <AssetSummaryCard
                  key={symbol}
                  symbol={symbol}
                  price={price}
                  candleHistory={candleHistories1m[symbol] ?? []}
                  prediction={predictions[symbol] ?? null}
                  status={analysisStates[symbol]}
                  error={lastErrors[symbol] ?? null}
                  isDataSufficient={isDataSufficient}
                  lastAdviceAt={lastAdviceAt[symbol]}
                />
              );
            })}
          </div>
        </section>

        {/* Portfolio & Trading Activity */}
        <section className="grid grid-cols-1 xl:grid-cols-4 gap-6">
          <div className="xl:col-span-2">
            <PortfolioPanel portfolio={portfolio} trades={tradeLog} />
          </div>
          <div className="xl:col-span-2 space-y-6">
            <AnalysisMonitor predictions={predictions} portfolio={portfolio} trades={tradeLog} />
            {/* Optimized responsive layout for better space usage */}
            <div className="space-y-4">
              {/* Top row - Market data and trade log */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <MarketIndicators assets={assets} />
                <TradeLog trades={tradeLog.slice(0, 8)} />
              </div>

              {/* AI Update Timer - Single component */}
              <div className="flex justify-center">
                <div className="w-full max-w-md">
                  <AIUpdateTimer
                    lastAdviceAt={lastAdviceAt}
                    intervalMs={ADVICE_INTERVAL_MS}
                    symbols={TRADABLE_ASSET_SYMBOLS}
                  />
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
};

export default App;